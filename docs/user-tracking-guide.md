# Dify 用户跟踪和监控指南

本指南介绍如何在不需要登录的聊天系统中实现用户识别和使用情况监控。

## 概述

Dify 提供了多种方式来跟踪和监控不同用户的使用情况：

1. **前端用户输入组件** - 在聊天界面添加用户标识输入
2. **工作流用户验证** - 通过工作流节点处理用户身份
3. **用户分析面板** - 统计和可视化用户使用数据
4. **URL参数传递** - 通过系统变量传递用户信息

## 方案一：前端用户输入组件

### 1. 简单用户输入组件

使用 `SimpleUserInput` 组件在聊天界面添加用户输入：

```tsx
import SimpleUserInput from '@/app/components/base/chat/simple-user-input'

// 在聊天界面中使用
<SimpleUserInput 
  onUserIdChange={(userId) => console.log('用户ID:', userId)}
  placeholder="请输入您的用户名"
  showLabel={true}
/>
```

### 2. 完整用户标识组件

使用 `UserIdentifier` 组件提供更丰富的用户管理功能：

```tsx
import UserIdentifier from '@/app/components/base/chat/chat-with-history/user-identifier'

// 在输入表单中使用
<UserIdentifier 
  onUserIdChange={(userId) => handleUserChange(userId)}
  className="mb-4"
/>
```

### 特性

- 自动保存用户ID到 localStorage
- 更新URL参数 `sys.user_id` 
- 支持编辑和清除用户信息
- 多语言支持

## 方案二：工作流用户验证

### 1. 用户验证节点

在工作流中添加用户验证节点来处理用户身份：

```python
# 节点配置示例
{
  "type": "user-validator",
  "config": {
    "user_id_variable": {
      "variable": "sys.user_id",
      "value_selector": ["sys", "user_id"]
    },
    "require_user_id": false,
    "default_user_message": "请输入您的用户名以继续",
    "anonymous_user_message": "您正在以匿名用户身份使用"
  }
}
```

### 2. 输出变量

用户验证节点提供以下输出变量：

- `has_user_id`: 是否有用户ID (boolean)
- `user_id`: 用户ID字符串
- `is_anonymous`: 是否为匿名用户 (boolean)
- `message`: 给用户的提示消息
- `should_prompt`: 是否需要提示用户输入

### 3. 工作流示例

```
开始 → 用户验证 → 条件判断
                    ├─ 有用户ID → 个性化回复
                    └─ 无用户ID → 通用回复/提示输入
```

## 方案三：用户分析面板

### 1. 集成用户分析组件

在应用概览页面添加用户分析：

```tsx
import UserAnalytics from '@/app/components/app/overview/user-analytics'

// 在概览页面使用
<UserAnalytics appId={appId} />
```

### 2. 分析数据

用户分析面板提供：

- **概览指标**：总用户数、今日活跃用户、总对话数、总消息数
- **时间范围选择**：1天、7天、30天、90天
- **每日活跃用户图表**：柱状图显示用户活跃趋势
- **用户活跃度分布**：显示最活跃的用户
- **详细用户列表**：用户ID、对话数、消息数、最后活跃时间

### 3. API端点

后端提供以下API：

- `GET /apps/{app_id}/user-analytics?range=7d` - 获取用户分析数据
- `GET /apps/{app_id}/users/{user_id}` - 获取特定用户详情

## 方案四：URL参数传递

### 1. 系统变量

通过URL参数传递用户信息：

```
https://your-app.com/chat?sys.user_id=base64_encoded_user_id
```

### 2. 前端处理

```typescript
// 获取系统变量
async function getProcessedSystemVariablesFromUrlParams(): Promise<Record<string, any>> {
  const urlParams = new URLSearchParams(window.location.search)
  const systemVariables: Record<string, any> = {}
  
  for (const [key, value] of urlParams.entries()) {
    if (key.startsWith('sys.')) {
      systemVariables[key.slice(4)] = await decodeBase64AndDecompress(decodeURIComponent(value))
    }
  }
  
  return systemVariables
}
```

### 3. 后端识别

后端通过 `from_end_user_id` 字段关联用户：

```python
# 在消息创建时关联用户
message = Message(
    app_id=app.id,
    from_end_user_id=end_user.id,  # 用户ID
    # ... 其他字段
)
```

## 实施建议

### 1. 渐进式实施

1. **第一阶段**：添加简单用户输入组件
2. **第二阶段**：集成用户分析面板
3. **第三阶段**：在工作流中添加用户验证逻辑

### 2. 用户体验优化

- 用户ID输入设为可选，避免强制要求
- 提供清晰的说明，解释为什么需要用户标识
- 支持匿名用户，不影响基本功能使用
- 记住用户输入，避免重复输入

### 3. 数据隐私

- 明确告知用户数据收集目的
- 提供清除用户数据的选项
- 遵循相关隐私法规要求

### 4. 技术考虑

- 用户ID使用Base64编码传递
- 支持自定义用户标识格式
- 考虑用户ID的唯一性和持久性
- 实现用户数据的导出和删除功能

## 监控指标

### 关键指标

1. **用户活跃度**
   - 日活跃用户数 (DAU)
   - 周活跃用户数 (WAU)
   - 月活跃用户数 (MAU)

2. **使用深度**
   - 平均对话数/用户
   - 平均消息数/用户
   - 会话时长分布

3. **用户留存**
   - 新用户留存率
   - 回访用户比例
   - 用户生命周期

### 报表示例

```sql
-- 每日活跃用户统计
SELECT 
    DATE(created_at) as date,
    COUNT(DISTINCT from_end_user_id) as active_users
FROM messages 
WHERE app_id = ? 
    AND created_at >= ?
GROUP BY DATE(created_at)
ORDER BY date;

-- 用户使用情况排行
SELECT 
    eu.external_user_id,
    COUNT(DISTINCT c.id) as conversation_count,
    COUNT(m.id) as message_count,
    MAX(m.created_at) as last_active
FROM end_users eu
LEFT JOIN conversations c ON c.from_end_user_id = eu.id
LEFT JOIN messages m ON m.from_end_user_id = eu.id
WHERE eu.app_id = ?
GROUP BY eu.id
ORDER BY message_count DESC;
```

## 故障排除

### 常见问题

1. **用户ID未保存**
   - 检查localStorage是否可用
   - 确认URL参数格式正确

2. **统计数据不准确**
   - 验证数据库查询逻辑
   - 检查时区设置

3. **用户重复计算**
   - 确保用户ID的唯一性
   - 处理匿名用户的去重逻辑

### 调试工具

```javascript
// 检查当前用户ID
console.log('Current User ID:', localStorage.getItem('chat_user_id'))

// 检查URL参数
console.log('URL Params:', new URLSearchParams(window.location.search))

// 检查系统变量
getProcessedSystemVariablesFromUrlParams().then(vars => {
  console.log('System Variables:', vars)
})
```

## 总结

通过以上方案，您可以在不需要用户登录的情况下有效跟踪和监控不同用户的使用情况。建议根据实际需求选择合适的方案组合，并注意用户体验和数据隐私的平衡。
