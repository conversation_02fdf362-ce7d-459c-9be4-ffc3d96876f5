{"human_prefix": "Human", "assistant_prefix": "Assistant", "context_prompt": "Use the following context as your learned knowledge, inside <context></context> XML tags.\n\n<context>\n{{#context#}}\n</context>\n\nWhen answer to user:\n- If you don't know, just say that you don't know.\n- If you don't know when you are not sure, ask for clarification.\nAvoid mentioning that you obtained the information from the context.\nAnd answer according to the language of the user's question.\n\n", "histories_prompt": "Here is the chat histories between human and assistant, inside <histories></histories> XML tags.\n\n<histories>\n{{#histories#}}\n</histories>\n\n", "system_prompt_orders": ["context_prompt", "pre_prompt", "histories_prompt"], "query_prompt": "\n\nHuman: {{#query#}}\n\nAssistant: ", "stops": ["\nHuman:", "</histories>"]}