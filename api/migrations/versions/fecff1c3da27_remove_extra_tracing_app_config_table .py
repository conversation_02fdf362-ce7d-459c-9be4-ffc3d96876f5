"""remove extra tracing app config table and add idx_dataset_permissions_tenant_id

Revision ID: fecff1c3da27
Revises: 408176b91ad3
Create Date: 2024-07-19 12:03:21.217463

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fecff1c3da27'
down_revision = '408176b91ad3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tracing_app_configs')

    # idx_dataset_permissions_tenant_id
    with op.batch_alter_table('dataset_permissions', schema=None) as batch_op:
        batch_op.create_index('idx_dataset_permissions_tenant_id', ['tenant_id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'tracing_app_configs',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('app_id', postgresql.UUID(), nullable=False),
        sa.Column('tracing_provider', sa.String(length=255), nullable=True),
        sa.Column('tracing_config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column(
            'created_at', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=False
        ),
        sa.Column(
            'updated_at', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=False
        ),
        sa.PrimaryKeyConstraint('id', name='tracing_app_config_pkey')
    )

    with op.batch_alter_table('dataset_permissions', schema=None) as batch_op:
        batch_op.drop_index('idx_dataset_permissions_tenant_id')

    # ### end Alembic commands ###
