"""add-annotation-histoiry-score

Revision ID: 46976cc39132
Revises: e1901f623fd0
Create Date: 2023-12-13 04:39:59.302971

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '46976cc39132'
down_revision = 'e1901f623fd0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_annotation_hit_histories', schema=None) as batch_op:
        batch_op.add_column(sa.Column('score', sa.Float(), server_default=sa.text('0'), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_annotation_hit_histories', schema=None) as batch_op:
        batch_op.drop_column('score')

    # ### end Alembic commands ###
