"""add_anntation_history_message_id

Revision ID: f2a6fc85e260
Revises: 46976cc39132
Create Date: 2023-12-13 11:09:29.329584

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f2a6fc85e260'
down_revision = '46976cc39132'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_annotation_hit_histories', schema=None) as batch_op:
        batch_op.add_column(sa.Column('message_id', postgresql.UUID(), nullable=False))
        batch_op.create_index('app_annotation_hit_histories_message_idx', ['message_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_annotation_hit_histories', schema=None) as batch_op:
        batch_op.drop_index('app_annotation_hit_histories_message_idx')
        batch_op.drop_column('message_id')

    # ### end Alembic commands ###
