"""add-tool-index

Revision ID: dfb3b7f477da
Revises: b24be59fbb04
Create Date: 2024-01-24 02:17:01.631635

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = 'dfb3b7f477da'
down_revision = 'b24be59fbb04'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.create_unique_constraint('unique_api_tool_provider', ['name', 'tenant_id'])

    with op.batch_alter_table('tool_files', schema=None) as batch_op:
        batch_op.create_index('tool_file_conversation_id_idx', ['conversation_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_files', schema=None) as batch_op:
        batch_op.drop_index('tool_file_conversation_id_idx')

    with op.batch_alter_table('tool_api_providers', schema=None) as batch_op:
        batch_op.drop_constraint('unique_api_tool_provider', type_='unique')

    # ### end Alembic commands ###
