"""add created by role

Revision ID: 9f4e3427ea84
Revises: 64b051264f32
Create Date: 2023-05-17 17:29:01.060435

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '9f4e3427ea84'
down_revision = '64b051264f32'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('pinned_conversations', schema=None) as batch_op:
        batch_op.add_column(sa.Column('created_by_role', sa.String(length=255), server_default=sa.text("'end_user'::character varying"), nullable=False))
        batch_op.drop_index('pinned_conversation_conversation_idx')
        batch_op.create_index('pinned_conversation_conversation_idx', ['app_id', 'conversation_id', 'created_by_role', 'created_by'], unique=False)

    with op.batch_alter_table('saved_messages', schema=None) as batch_op:
        batch_op.add_column(sa.Column('created_by_role', sa.String(length=255), server_default=sa.text("'end_user'::character varying"), nullable=False))
        batch_op.drop_index('saved_message_message_idx')
        batch_op.create_index('saved_message_message_idx', ['app_id', 'message_id', 'created_by_role', 'created_by'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('saved_messages', schema=None) as batch_op:
        batch_op.drop_index('saved_message_message_idx')
        batch_op.create_index('saved_message_message_idx', ['app_id', 'message_id', 'created_by'], unique=False)
        batch_op.drop_column('created_by_role')

    with op.batch_alter_table('pinned_conversations', schema=None) as batch_op:
        batch_op.drop_index('pinned_conversation_conversation_idx')
        batch_op.create_index('pinned_conversation_conversation_idx', ['app_id', 'conversation_id', 'created_by'], unique=False)
        batch_op.drop_column('created_by_role')

    # ### end Alembic commands ###
