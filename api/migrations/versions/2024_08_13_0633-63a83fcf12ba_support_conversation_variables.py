"""support conversation variables

Revision ID: 63a83fcf12ba
Revises: 1787fbae959a
Create Date: 2024-08-13 06:33:07.950379

"""
import sqlalchemy as sa
from alembic import op

import models as models

# revision identifiers, used by Alembic.
revision = '63a83fcf12ba'
down_revision = '1787fbae959a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('workflow__conversation_variables',
    sa.Column('id', models.types.StringUUID(), nullable=False),
    sa.Column('conversation_id', models.types.StringUUID(), nullable=False),
    sa.Column('app_id', models.types.StringUUID(), nullable=False),
    sa.Column('data', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.PrimaryKeyConstraint('id', 'conversation_id', name=op.f('workflow__conversation_variables_pkey'))
    )
    with op.batch_alter_table('workflow__conversation_variables', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('workflow__conversation_variables_app_id_idx'), ['app_id'], unique=False)
        batch_op.create_index(batch_op.f('workflow__conversation_variables_created_at_idx'), ['created_at'], unique=False)

    with op.batch_alter_table('workflows', schema=None) as batch_op:
        batch_op.add_column(sa.Column('conversation_variables', sa.Text(), server_default='{}', nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('workflows', schema=None) as batch_op:
        batch_op.drop_column('conversation_variables')

    with op.batch_alter_table('workflow__conversation_variables', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('workflow__conversation_variables_created_at_idx'))
        batch_op.drop_index(batch_op.f('workflow__conversation_variables_app_id_idx'))

    op.drop_table('workflow__conversation_variables')
    # ### end Alembic commands ###
