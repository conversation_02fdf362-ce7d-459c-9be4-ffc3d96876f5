"""add dataset query variable at app model configs.

Revision ID: ab23c11305d4
Revises: 6e2cfb077b04
Create Date: 2023-09-26 12:22:59.044088

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'ab23c11305d4'
down_revision = '6e2cfb077b04'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.add_column(sa.Column('dataset_query_variable', sa.String(length=255), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.drop_column('dataset_query_variable')

    # ### end Alembic commands ###
