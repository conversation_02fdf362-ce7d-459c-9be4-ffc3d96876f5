"""add provider order

Revision ID: bf0aec5ba2cf
Revises: e35ed59becda
Create Date: 2023-08-10 00:03:44.273430

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bf0aec5ba2cf'
down_revision = 'e35ed59becda'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('provider_orders',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
    sa.Column('tenant_id', postgresql.UUID(), nullable=False),
    sa.Column('provider_name', sa.String(length=40), nullable=False),
    sa.Column('account_id', postgresql.UUID(), nullable=False),
    sa.Column('payment_product_id', sa.String(length=191), nullable=False),
    sa.Column('payment_id', sa.String(length=191), nullable=True),
    sa.Column('transaction_id', sa.String(length=191), nullable=True),
    sa.Column('quantity', sa.Integer(), server_default=sa.text('1'), nullable=False),
    sa.Column('currency', sa.String(length=40), nullable=True),
    sa.Column('total_amount', sa.Integer(), nullable=True),
    sa.Column('payment_status', sa.String(length=40), server_default=sa.text("'wait_pay'::character varying"), nullable=False),
    sa.Column('paid_at', sa.DateTime(), nullable=True),
    sa.Column('pay_failed_at', sa.DateTime(), nullable=True),
    sa.Column('refunded_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
    sa.PrimaryKeyConstraint('id', name='provider_order_pkey')
    )
    with op.batch_alter_table('provider_orders', schema=None) as batch_op:
        batch_op.create_index('provider_order_tenant_provider_idx', ['tenant_id', 'provider_name'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('provider_orders', schema=None) as batch_op:
        batch_op.drop_index('provider_order_tenant_provider_idx')

    op.drop_table('provider_orders')
    # ### end Alembic commands ###
