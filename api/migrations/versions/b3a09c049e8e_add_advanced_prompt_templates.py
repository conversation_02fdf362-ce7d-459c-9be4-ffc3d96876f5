"""add advanced prompt templates

Revision ID: b3a09c049e8e
Revises: 2e9819ca5b28
Create Date: 2023-10-10 15:23:23.395420

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'b3a09c049e8e'
down_revision = '2e9819ca5b28'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.add_column(sa.Column('prompt_type', sa.String(length=255), nullable=False, server_default='simple'))
        batch_op.add_column(sa.Column('chat_prompt_config', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('completion_prompt_config', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('dataset_configs', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('app_model_configs', schema=None) as batch_op:
        batch_op.drop_column('dataset_configs')
        batch_op.drop_column('completion_prompt_config')
        batch_op.drop_column('chat_prompt_config')
        batch_op.drop_column('prompt_type')

    # ### end Alembic commands ###
