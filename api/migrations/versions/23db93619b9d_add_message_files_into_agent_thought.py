"""add message files into agent thought

Revision ID: 23db93619b9d
Revises: 8ae9bc661daa
Create Date: 2024-01-18 08:46:37.302657

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '23db93619b9d'
down_revision = '8ae9bc661daa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('message_files', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('message_agent_thoughts', schema=None) as batch_op:
        batch_op.drop_column('message_files')

    # ### end Alembic commands ###
