"""add app tracing

Revision ID: 2a3aebbbf4bb
Revises: c031d46af369
Create Date: 2024-06-17 10:08:54.803701

"""
import sqlalchemy as sa
from alembic import op

import models as models

# revision identifiers, used by Alembic.
revision = '2a3aebbbf4bb'
down_revision = 'c031d46af369'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.add_column(sa.Column('tracing', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('apps', schema=None) as batch_op:
        batch_op.drop_column('tracing')

    # ### end Alembic commands ###
