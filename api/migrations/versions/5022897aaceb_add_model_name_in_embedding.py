"""add model name in embedding

Revision ID: 5022897aaceb
Revises: bf0aec5ba2cf
Create Date: 2023-08-11 14:38:15.499460

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '5022897aaceb'
down_revision = 'bf0aec5ba2cf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('embeddings', schema=None) as batch_op:
        batch_op.add_column(sa.Column('model_name', sa.String(length=40), server_default=sa.text("'text-embedding-ada-002'::character varying"), nullable=False))
        batch_op.drop_constraint('embedding_hash_idx', type_='unique')
        batch_op.create_unique_constraint('embedding_hash_idx', ['model_name', 'hash'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('embeddings', schema=None) as batch_op:
        batch_op.drop_constraint('embedding_hash_idx', type_='unique')
        batch_op.create_unique_constraint('embedding_hash_idx', ['hash'])
        batch_op.drop_column('model_name')

    # ### end Alembic commands ###
