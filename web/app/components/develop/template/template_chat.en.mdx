import { CodeGroup } from '../code.tsx'
import { Row, Col, Properties, Property, Heading, SubProperty, Paragraph } from '../md.tsx'

# Chat App API

Chat applications support session persistence, allowing previous chat history to be used as context for responses. This can be applicable for chatbot, customer service AI, etc.

<div>
  ### Base URL
  <CodeGroup title="Code" targetCode={props.appDetail.api_base_url}>
    ```javascript
    ```
  </CodeGroup>

  ### Authentication

  The Service API uses `API-Key` authentication.
  <i>**Strongly recommend storing your API Key on the server-side, not shared or stored on the client-side, to avoid possible API-Key leakage that can lead to serious consequences.**</i>

  For all API requests, include your API Key in the `Authorization`HTTP Header, as shown below:

  <CodeGroup title="Code">
    ```javascript
      Authorization: Bearer {API_KEY}

    ```
  </CodeGroup>
</div>

---

<Heading
  url='/chat-messages'
  method='POST'
  title='Send Chat Message'
  name='#Send-Chat-Message'
/>
<Row>
  <Col>
    Send a request to the chat application.

    ### Request Body

    <Properties>
      <Property name='query' type='string' key='query'>
        User Input/Question content
      </Property>
      <Property name='inputs' type='object' key='inputs'>
          Allows the entry of various variable values defined by the App.
          The `inputs` parameter contains multiple key/value pairs, with each key corresponding to a specific variable and each value being the specific value for that variable. Default `{}`
      </Property>
      <Property name='response_mode' type='string' key='response_mode'>
        The mode of response return, supporting:
        - `streaming` Streaming mode (recommended), implements a typewriter-like output through SSE ([Server-Sent Events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events)).
        - `blocking` Blocking mode, returns result after execution is complete. (Requests may be interrupted if the process is long)
        Due to Cloudflare restrictions, the request will be interrupted without a return after 100 seconds.
        <i>Note: blocking mode is not supported in Agent Assistant mode</i>
      </Property>
      <Property name='user' type='string' key='user'>
          User identifier, used to define the identity of the end-user for retrieval and statistics.
          Should be uniquely defined by the developer within the application.
      </Property>
      <Property name='conversation_id' type='string' key='conversation_id'>
      Conversation ID, to continue the conversation based on previous chat records, it is necessary to pass the previous message's conversation_id.
      </Property>
      <Property name='files' type='array[object]' key='files'>
          File list, suitable for inputting files (images) combined with text understanding and answering questions, available only when the model supports Vision capability.
          - `type` (string) Supported type: `image` (currently only supports image type)
          - `transfer_method` (string) Transfer method, `remote_url` for image URL / `local_file` for file upload
          - `url` (string) Image URL (when the transfer method is `remote_url`)
          - `upload_file_id` (string) Uploaded file ID, which must be obtained by uploading through the File Upload API in advance (when the transfer method is `local_file`)
      </Property>
      <Property name='auto_generate_name' type='bool' key='auto_generate_name'>
      Auto-generate title, default is `true`.
      If set to `false`, can achieve async title generation by calling the conversation rename API and setting `auto_generate` to `true`.
      </Property>
    </Properties>

    ### Response
    When response_mode is blocking, return a CompletionResponse object.
    When response_mode is streaming, return a ChunkCompletionResponse stream.

    ### ChatCompletionResponse
    Returns the complete App result, `Content-Type` is `application/json`.
    - `event` (string) 事件类型，固定为 `message`
    - `task_id` (string) 任务 ID，用于请求跟踪和下方的停止响应接口
    - `id` (string) 唯一ID
    - `message_id` (string) Unique message ID
    - `conversation_id` (string) Conversation ID
    - `mode` (string) App mode, fixed as `chat`
    - `answer` (string) Complete response content
    - `metadata` (object) Metadata
      - `usage` (Usage) Model usage information
      - `retriever_resources` (array[RetrieverResource]) Citation and Attribution List
    - `created_at` (int) Message creation timestamp, e.g., 1705395332

    ### ChunkChatCompletionResponse
    Returns the stream chunks outputted by the App, `Content-Type` is `text/event-stream`.
    Each streaming chunk starts with `data:`, separated by two newline characters `\n\n`, as shown below:
    <CodeGroup>
    ```streaming {{ title: 'Response' }}
    data: {"event": "message", "task_id": "900bbd43-dc0b-4383-a372-aa6e6c414227", "id": "663c5084-a254-4040-8ad3-51f2a3c1a77c", "answer": "Hi", "created_at": 1705398420}\n\n
    ```
    </CodeGroup>
    The structure of the streaming chunks varies depending on the `event`:
    - `event: message` LLM returns text chunk event, i.e., the complete text is output in a chunked fashion.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `conversation_id` (string) Conversation ID
      - `answer` (string) LLM returned text chunk content
      - `created_at` (int) Creation timestamp, e.g., 1705395332
    - `event: agent_message` LLM returns text chunk event, i.e., with Agent Assistant enabled, the complete text is output in a chunked fashion (Only supported in Agent mode)
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `conversation_id` (string) Conversation ID
      - `answer` (string) LLM returned text chunk content
      - `created_at` (int) Creation timestamp, e.g., 1705395332
    - `event: tts_message` TTS audio stream event, that is, speech synthesis output. The content is an audio block in Mp3 format, encoded as a base64 string. When playing, simply decode the base64 and feed it into the player. (This message is available only when auto-play is enabled)
      - `task_id` (string) Task ID, used for request tracking and the stop response interface below
      - `message_id` (string) Unique message ID
      - `audio` (string) The audio after speech synthesis, encoded in base64 text content, when playing, simply decode the base64 and feed it into the player
      - `created_at` (int) Creation timestamp, e.g.: 1705395332
    - `event: tts_message_end` TTS audio stream end event, receiving this event indicates the end of the audio stream.
      - `task_id` (string) Task ID, used for request tracking and the stop response interface below
      - `message_id` (string) Unique message ID
      - `audio` (string) The end event has no audio, so this is an empty string
      - `created_at` (int) Creation timestamp, e.g.: 1705395332
    - `event: agent_thought` thought of Agent, contains the thought of LLM, input and output of tool calls (Only supported in Agent mode)
      - `id` (string) Agent thought ID, every iteration has a unique agent thought ID
      - `task_id` (string)  Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `position` (int) Position of current agent thought, each message may have multiple thoughts in order.
      - `thought` (string) What LLM is thinking about
      - `observation` (string) Response from tool calls
      - `tool` (string) A list of tools represents which tools are called，split by ;
      - `tool_input` (string) Input of tools in JSON format. Like: `{"dalle3": {"prompt": "a cute cat"}}`.
      - `created_at` (int) Creation timestamp, e.g., 1705395332
      - `message_files` (array[string])  Refer to message_file event
        - `file_id` (string) File ID
      - `conversation_id` (string) Conversation ID
    - `event: message_file` Message file event, a new file has created by tool
      - `id` (string) File unique ID
      - `type` (string) File type，only allow "image" currently
      - `belongs_to` (string) Belongs to, it will only be an 'assistant' here
      - `url` (string) Remote url of file
      - `conversation_id`  (string) Conversation ID
    - `event: message_end` Message end event, receiving this event means streaming has ended.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `conversation_id` (string) Conversation ID
      - `metadata` (object) Metadata
        - `usage` (Usage) Model usage information
        - `retriever_resources` (array[RetrieverResource]) Citation and Attribution List
    - `event: message_replace` Message content replacement event.
      When output content moderation is enabled, if the content is flagged, then the message content will be replaced with a preset reply through this event.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `conversation_id` (string) Conversation ID
      - `answer` (string) Replacement content (directly replaces all LLM reply text)
      - `created_at` (int) Creation timestamp, e.g., 1705395332
    - `event: error`
      Exceptions that occur during the streaming process will be output in the form of stream events, and reception of an error event will end the stream.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `status` (int) HTTP status code
      - `code` (string) Error code
      - `message` (string) Error message
    - `event: ping` Ping event every 10 seconds to keep the connection alive.

    ### Errors
    - 404, Conversation does not exists
    - 400, `invalid_param`, abnormal parameter input
    - 400, `app_unavailable`, App configuration unavailable
    - 400, `provider_not_initialize`, no available model credential configuration
    - 400, `provider_quota_exceeded`, model invocation quota insufficient
    - 400, `model_currently_not_support`, current model unavailable
    - 400, `completion_request_error`, text generation failed
    - 500, internal server error

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/chat-messages" targetCode={`curl -X POST '${props.appDetail.api_base_url}/chat-messages' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n    "inputs": ${JSON.stringify(props.inputs)},\n    "query": "What are the specs of the iPhone 13 Pro Max?",\n    "response_mode": "streaming",\n    "conversation_id": "",\n    "user": "abc-123",\n    "files": [\n      {\n        "type": "image",\n        "transfer_method": "remote_url",\n        "url": "https://cloud.dify.ai/logo/logo-site.png"\n      }\n    ]\n}'`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/chat-messages' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "inputs": {},
        "query": "eh",
        "response_mode": "streaming",
        "conversation_id": "1c7e55fb-1ba2-4e10-81b5-30addcea2276",
        "user": "abc-123"
    }'
    ```
    </CodeGroup>
    ### Blocking Mode
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
        "event": "message",
        "task_id": "c3800678-a077-43df-a102-53f23ed20b88", 
        "id": "9da23599-e713-473b-982c-4328d4f5c78a",
        "message_id": "9da23599-e713-473b-982c-4328d4f5c78a",
        "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2",
        "mode": "chat",
        "answer": "iPhone 13 Pro Max specs are listed here:...",
        "metadata": {
            "usage": {
                "prompt_tokens": 1033,
                "prompt_unit_price": "0.001",
                "prompt_price_unit": "0.001",
                "prompt_price": "0.0010330",
                "completion_tokens": 128,
                "completion_unit_price": "0.002",
                "completion_price_unit": "0.001",
                "completion_price": "0.0002560",
                "total_tokens": 1161,
                "total_price": "0.0012890",
                "currency": "USD",
                "latency": 0.7682376249867957
            },
            "retriever_resources": [
                {
                    "position": 1,
                    "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb",
                    "dataset_name": "iPhone",
                    "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00",
                    "document_name": "iPhone List",
                    "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a",
                    "score": 0.98457545,
                    "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""
                }
            ]
        },
        "created_at": 1705407629
    }
    ```
    </CodeGroup>
    ### Streaming Mode ( Basic Assistant )
    <CodeGroup title="Response">
    ```streaming {{ title: 'Response' }}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " I", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": "'m", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " glad", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " to", "created_at": 1679586595}
      data: {"event": "message", "message_id" : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " meet", "created_at": 1679586595}
      data: {"event": "message", "message_id" : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " you", "created_at": 1679586595}
      data: {"event": "message_end", "id": "5e52ce04-874b-4d27-9045-b3bc80def685", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "metadata": {"usage": {"prompt_tokens": 1033, "prompt_unit_price": "0.001", "prompt_price_unit": "0.001", "prompt_price": "0.0010330", "completion_tokens": 135, "completion_unit_price": "0.002", "completion_price_unit": "0.001", "completion_price": "0.0002700", "total_tokens": 1168, "total_price": "0.0013030", "currency": "USD", "latency": 1.381760165997548}, "retriever_resources": [{"position": 1, "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb", "dataset_name": "iPhone", "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00", "document_name": "iPhone List", "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a", "score": 0.98457545, "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""}]}}
      data: {"event": "tts_message", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": "qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq"}
      data: {"event": "tts_message_end", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": ""}
    ```
    </CodeGroup>
    ### Response Example(Agent Assistant)
    <CodeGroup title="Response">
    ```streaming {{ title: 'Response' }}
    data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " I", "created_at": 1679586595}
    data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": "'m", "created_at": 1679586595}
    data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " glad", "created_at": 1679586595}
    data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " to", "created_at": 1679586595}
    data: {"event": "message", "message_id" : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " meet", "created_at": 1679586595}
    data: {"event": "message", "message_id" : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " you", "created_at": 1679586595}
    data: {"event": "message_end", "id": "5e52ce04-874b-4d27-9045-b3bc80def685", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "metadata": {"usage": {"prompt_tokens": 1033, "prompt_unit_price": "0.001", "prompt_price_unit": "0.001", "prompt_price": "0.0010330", "completion_tokens": 135, "completion_unit_price": "0.002", "completion_price_unit": "0.001", "completion_price": "0.0002700", "total_tokens": 1168, "total_price": "0.0013030", "currency": "USD", "latency": 1.381760165997548}, "retriever_resources": [{"position": 1, "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb", "dataset_name": "iPhone", "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00", "document_name": "iPhone List", "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a", "score": 0.98457545, "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""}]}}
    data: {"event": "tts_message", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": "qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq"}
    data: {"event": "tts_message_end", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": ""}
  ```
    </CodeGroup>
  </Col>
</Row>

---
<Heading
  url='/files/upload'
  method='POST'
  title='File Upload'
  name='#file-upload'
/>
<Row>
  <Col>
  Upload a file (currently only images are supported) for use when sending messages, enabling multimodal understanding of images and text.
  Supports png, jpg, jpeg, webp, gif formats.
  Uploaded files are for use by the current end-user only.

  ### Request Body
  This interface requires a `multipart/form-data` request.
  - `file` (File) Required
    The file to be uploaded.
  - `user` (string) Required
    User identifier, defined by the developer's rules, must be unique within the application.

  ### Response
  After a successful upload, the server will return the file's ID and related information.
  - `id` (uuid) ID
  - `name` (string) File name
  - `size` (int) File size (bytes)
  - `extension` (string) File extension
  - `mime_type` (string) File mime-type
  - `created_by` (uuid) End-user ID
  - `created_at` (timestamp) Creation timestamp, e.g., 1705395332

  ### Errors
  - 400, `no_file_uploaded`, a file must be provided
  - 400, `too_many_files`, currently only one file is accepted
  - 400, `unsupported_preview`, the file does not support preview
  - 400, `unsupported_estimate`, the file does not support estimation
  - 413, `file_too_large`, the file is too large
  - 415, `unsupported_file_type`, unsupported extension, currently only document files are accepted
  - 503, `s3_connection_failed`, unable to connect to S3 service
  - 503, `s3_permission_denied`, no permission to upload files to S3
  - 503, `s3_file_too_large`, file exceeds S3 size limit
  - 500, internal server error


  </Col>
  <Col sticky>
  ### Request Example
  <CodeGroup title="Request" tag="POST" label="/files/upload" targetCode={`curl -X POST '${props.appDetail.api_base_url}/files/upload' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'file=@localfile;type=image/[png|jpeg|jpg|webp|gif]' \\\n--form 'user=abc-123'`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/files/upload' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'file=@"/path/to/file"'
    ```

    </CodeGroup>


  ### Response Example
  <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "72fa9618-8f89-4a37-9b33-7e1178a24a67",
      "name": "example.png",
      "size": 1024,
      "extension": "png",
      "mime_type": "image/png",
      "created_by": "6ad1ab0a-73ff-4ac1-b9e4-cdb312f71f13",
      "created_at": 1577836800,
    }
  ```
  </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/chat-messages/:task_id/stop'
  method='POST'
  title='Stop Generate'
  name='#stop-generatebacks'
/>
<Row>
  <Col>
  Only supported in streaming mode.
  ### Path
  - `task_id` (string) Task ID, can be obtained from the streaming chunk return
  ### Request Body
  - `user` (string) Required
    User identifier, used to define the identity of the end-user, must be consistent with the user passed in the send message interface.
  ### Response
  - `result` (string) Always returns "success"
  </Col>
  <Col sticky>
  ### Request Example
  <CodeGroup title="Request" tag="POST" label="/chat-messages/:task_id/stop" targetCode={`curl -X POST '${props.appDetail.api_base_url}/chat-messages/:task_id/stop' \\\n-H 'Authorization: Bearer {api_key}' \\\n-H 'Content-Type: application/json' \\\n--data-raw '{"user": "abc-123"}'`}>
    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/chat-messages/:task_id/stop' \
    -H 'Authorization: Bearer {api_key}' \
    -H 'Content-Type: application/json' \
    --data-raw '{
        "user": "abc-123"
    }'
    ```
    </CodeGroup>

    ### Response Example
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/messages/:message_id/feedbacks'
  method='POST'
  title='Message Feedback'
  name='#feedbacks'
/>
<Row>
  <Col>
    End-users can provide feedback messages, facilitating application developers to optimize expected outputs.

    ### Path
    <Properties>
      <Property name='message_id' type='string' key='message_id'>
       Message ID
      </Property>
    </Properties>

    ### Request Body

    <Properties>
      <Property name='rating' type='string' key='rating'>
        Upvote as `like`, downvote as `dislike`, revoke upvote as `null`
      </Property>
      <Property name='user' type='string' key='user'>
        User identifier, defined by the developer's rules, must be unique within the application.
      </Property>
      <Property name='content' type='string' key='content'>
        The specific content of message feedback.
      </Property>
    </Properties>

    ### Response
    - `result` (string) Always returns "success"
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/messages/:message_id/feedbacks" targetCode={`curl -X POST '${props.appDetail.api_base_url}/messages/:message_id/feedbacks \\\n --header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n    "rating": "like",\n    "user": "abc-123",\n    "content": "message feedback information"\n}'`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/messages/:message_id/feedbacks' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "rating": "like",
        "user": "abc-123",
        "content": "message feedback information"
    }'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/app/feedbacks'
  method='GET'
  title='Get feedbacks of application'
  name='#app-feedbacks'
/>
<Row>
  <Col>
    Get application's feedbacks.

    ### Query
    <Properties>
      <Property name='page' type='string' key='page'>
       （optional）pagination，default：1
      </Property>
    </Properties>

    <Properties>
      <Property name='limit' type='string' key='limit'>
       （optional） records per page default：20
      </Property>
    </Properties>

    ### Response
    - `data` (List) return apps feedback list.
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/app/feedbacks" targetCode={`curl -X GET '${props.appDetail.api_base_url}/app/feedbacks?page=1&limit=20'`}>

    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/app/feedbacks?page=1&limit=20' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
      {
          "data": [
              {
                  "id": "8c0fbed8-e2f9-49ff-9f0e-15a35bdd0e25",
                  "app_id": "f252d396-fe48-450e-94ec-e184218e7346",
                  "conversation_id": "2397604b-9deb-430e-b285-4726e51fd62d",
                  "message_id": "709c0b0f-0a96-4a4e-91a4-ec0889937b11",
                  "rating": "like",
                  "content": "message feedback information-3",
                  "from_source": "user",
                  "from_end_user_id": "********-9a1a-42c1-929c-01edb1d381d5",
                  "from_account_id": null,
                  "created_at": "2025-04-24T09:24:38",
                  "updated_at": "2025-04-24T09:24:38"
              }
          ]
      }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/messages/{message_id}/suggested'
  method='GET'
  title='Next Suggested Questions'
  name='#suggested'
/>
<Row>
  <Col>
    Get next questions suggestions for the current message

    ### Path Params

    <Properties>
      <Property name='message_id' type='string' key='message_id'>
        Message ID
      </Property>
    </Properties>

    ### Query
    <Properties>
      <Property name='user' type='string' key='user'>
        User identifier, used to define the identity of the end-user for retrieval and statistics.
        Should be uniquely defined by the developer within the application.
      </Property>
    </Properties>
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/messages/{message_id}/suggested" targetCode={`curl --location --request GET '${props.appDetail.api_base_url}/messages/{message_id}/suggested?user=abc-123& \\\n--header 'Authorization: Bearer ENTER-YOUR-SECRET-KEY' \\\n--header 'Content-Type: application/json'`}>

    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.appDetail.api_base_url}/messages/{message_id}/suggested' \
    --header 'Authorization: Bearer ENTER-YOUR-SECRET-KEY' \
    --header 'Content-Type: application/json' \
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success",
      "data": [
            "a",
            "b",
            "c"
        ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/messages'
  method='GET'
  title='Get Conversation History Messages'
  name='#messages'
/>
<Row>
  <Col>
    Returns historical chat records in a scrolling load format, with the first page returning the latest `{limit}` messages, i.e., in reverse order.

    ### Query

    <Properties>
      <Property name='conversation_id' type='string' key='conversation_id'>
        Conversation ID
      </Property>
      <Property name='user' type='string' key='user'>
        User identifier, used to define the identity of the end-user for retrieval and statistics.
        Should be uniquely defined by the developer within the application.
      </Property>
      <Property name='first_id' type='string' key='first_id'>
          The ID of the first chat record on the current page, default is null.
      </Property>
      <Property name='limit' type='int' key='limit'>
          How many chat history messages to return in one request, default is 20.
      </Property>
    </Properties>

    ### Response
    - `data` (array[object]) Message list
      - `id` (string) Message ID
      - `conversation_id` (string) Conversation ID
      - `inputs` (object) User input parameters.
      - `query` (string) User input / question content.
      - `message_files` (array[object]) Message files
        - `id` (string) ID
        - `type` (string) File type, image for images
        - `url` (string) Preview image URL
        - `belongs_to` (string) belongs to，user or assistant
      - `agent_thoughts` (array[object]) Agent thought（Empty if it's a Basic Assistant）
        - `id` (string) Agent thought ID, every iteration has a unique agent thought ID
        - `message_id` (string) Unique message ID
        - `position` (int) Position of current agent thought, each message may have multiple thoughts in order.
        - `thought` (string) What LLM is thinking about
        - `observation` (string) Response from tool calls
        - `tool` (string) A list of tools represents which tools are called，split by ;
        - `tool_input` (string) Input of tools in JSON format. Like: `{"dalle3": {"prompt": "a cute cat"}}`.
        - `created_at` (int) Creation timestamp, e.g., 1705395332
        - `message_files` (array[string])  Refer to message_file event
          - `file_id` (string) File ID
      - `answer` (string) Response message content
      - `created_at` (timestamp) Creation timestamp, e.g., 1705395332
      - `feedback` (object) Feedback information
        - `rating` (string) Upvote as `like` / Downvote as `dislike`
      - `retriever_resources` (array[RetrieverResource]) Citation and Attribution List
    - `has_more` (bool) Whether there is a next page
    - `limit` (int) Number of returned items, if input exceeds system limit, returns system limit amount

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/messages" targetCode={`curl -X GET '${props.appDetail.api_base_url}/messages?user=abc-123&conversation_id='\\\n --header 'Authorization: Bearer {api_key}'`}>

    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/messages?user=abc-123&conversation_id='
    --header 'Authorization: Bearer {api_key}'
    ```

    </CodeGroup>
    ### Response Example (Basic Assistant)
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "limit": 20,
      "has_more": false,
      "data": [
        {
            "id": "a076a87f-31e5-48dc-b452-0061adbbc922",
            "conversation_id": "cd78daf6-f9e4-4463-9ff2-54257230a0ce",
            "inputs": {
                "name": "dify"
            },
            "query": "iphone 13 pro",
            "answer": "The iPhone 13 Pro, released on September 24, 2021, features a 6.1-inch display with a resolution of 1170 x 2532. It is equipped with a Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard) processor, 6 GB of RAM, and offers storage options of 128 GB, 256 GB, 512 GB, and 1 TB. The camera is 12 MP, the battery capacity is 3095 mAh, and it runs on iOS 15.",
            "message_files": [],
            "feedback": null,
            "retriever_resources": [
                {
                    "position": 1,
                    "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb",
                    "dataset_name": "iPhone",
                    "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00",
                    "document_name": "iPhone List",
                    "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a",
                    "score": 0.98457545,
                    "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""
                }
            ],
            "agent_thoughts": [],
            "created_at": 1705569239,
        }
      ]
    }
    ```
    </CodeGroup>

    ### Response Example (Agent Assistant)
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
        "limit": 20,
        "has_more": false,
        "data": [
            {
                "id": "d35e006c-7c4d-458f-9142-be4930abdf94",
                "conversation_id": "957c068b-f258-4f89-ba10-6e8a0361c457",
                "inputs": {},
                "query": "draw a cat",
                "answer": "I have generated an image of a cat for you. Please check your messages to view the image.",
                "message_files": [
                    {
                        "id": "976990d2-5294-47e6-8f14-7356ba9d2d76",
                        "type": "image",
                        "url": "http://127.0.0.1:5001/files/tools/976990d2-5294-47e6-8f14-7356ba9d2d76.png?timestamp=1705988524&nonce=55df3f9f7311a9acd91bf074cd524092&sign=z43nMSO1L2HBvoqADLkRxr7Biz0fkjeDstnJiCK1zh8=",
                        "belongs_to": "assistant"
                    }
                ],
                "feedback": null,
                "retriever_resources": [],
                "created_at": 1705988187,
                "agent_thoughts": [
                    {
                        "id": "592c84cf-07ee-441c-9dcc-ffc66c033469",
                        "chain_id": null,
                        "message_id": "d35e006c-7c4d-458f-9142-be4930abdf94",
                        "position": 1,
                        "thought": "",
                        "tool": "dalle2",
                        "tool_input": "{\"dalle2\": {\"prompt\": \"cat\"}}",
                        "created_at": 1705988186,
                        "observation": "image has been created and sent to user already, you should tell user to check it now.",
                        "files": [
                            "976990d2-5294-47e6-8f14-7356ba9d2d76"
                        ]
                    },
                    {
                        "id": "73ead60d-2370-4780-b5ed-532d2762b0e5",
                        "chain_id": null,
                        "message_id": "d35e006c-7c4d-458f-9142-be4930abdf94",
                        "position": 2,
                        "thought": "I have generated an image of a cat for you. Please check your messages to view the image.",
                        "tool": "",
                        "tool_input": "",
                        "created_at": 1705988199,
                        "observation": "",
                        "files": []
                    }
                ]
            }
        ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/conversations'
  method='GET'
  title='Get Conversations'
  name='#conversations'
/>
<Row>
  <Col>
    Retrieve the conversation list for the current user, defaulting to the most recent 20 entries.

    ### Query

    <Properties>
      <Property name='user' type='string' key='user'>
          User identifier, used to define the identity of the end-user for retrieval and statistics.
          Should be uniquely defined by the developer within the application.
      </Property>
      <Property name='last_id' type='string' key='last_id'>
          (Optional) The ID of the last record on the current page, default is null.
      </Property>
      <Property name='limit' type='int' key='limit'>
          (Optional) How many records to return in one request, default is the most recent 20 entries. Maximum 100, minimum 1.
      </Property>
      <Property name='sort_by' type='string' key='sort_by'>
        (Optional) Sorting Field, Default: -updated_at (sorted in descending order by update time)
        - Available Values: created_at, -created_at, updated_at, -updated_at
        - The symbol before the field represents the order or reverse, "-" represents reverse order.
      </Property>
    </Properties>

    ### Response
    - `data` (array[object]) List of conversations
      - `id` (string) Conversation ID
      - `name` (string) Conversation name, by default, is a snippet of the first question asked by the user in the conversation.
      - `inputs` (object) User input parameters.
      - `status` (string) Conversation status
      - `introduction` (string) Introduction
      - `created_at` (timestamp) Creation timestamp, e.g., 1705395332
      - `updated_at` (timestamp) Update timestamp, e.g., 1705395332
    - `has_more` (bool)
    - `limit` (int) Number of entries returned, if input exceeds system limit, system limit number is returned

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/conversations" targetCode={`curl -X GET '${props.appDetail.api_base_url}/conversations?user=abc-123&last_id=&limit=20' \\\n --header 'Authorization: Bearer {api_key}'`}>

    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/conversations?user=abc-123&last_id=&limit=20' \
    --header 'Authorization: Bearer {api_key}'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "limit": 20,
      "has_more": false,
      "data": [
        {
          "id": "10799fb8-64f7-4296-bbf7-b42bfbe0ae54",
          "name": "New chat",
          "inputs": {
              "book": "book",
              "myName": "Lucy"
          },
          "status": "normal",
          "created_at": 1679667915,
          "updated_at": 1679667915
        },
        {
          "id": "hSIhXBhNe8X1d8Et"
          // ...
        }
      ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/conversations/:conversation_id'
  method='DELETE'
  title='Delete Conversation'
  name='#delete'
/>
<Row>
  <Col>
    Delete a conversation.

    ### Path
    - `conversation_id` (string) Conversation ID

    ### Request Body

    <Properties>
      <Property name='user' type='string' key='user'>
        The user identifier, defined by the developer, must ensure uniqueness within the application.
      </Property>
    </Properties>

    ### Response
    - `result` (string) Always returns "success"
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="DELETE" label="/conversations/:conversation_id" targetCode={`curl -X DELETE '${props.appDetail.api_base_url}/conversations/:conversation_id' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{ \n "user": "abc-123"\n}'`}>

    ```bash {{ title: 'cURL' }}
        curl -X DELETE '${props.appDetail.api_base_url}/conversations/{conversation_id}' \
        --header 'Content-Type: application/json' \
        --header 'Accept: application/json' \
        --header 'Authorization: Bearer {api_key}' \
        --data '{
            "user": "abc-123"
        }'
      ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```text {{ title: 'Response' }}
    204 No Content
    ```
    </CodeGroup>
  </Col>
</Row>

---
<Heading
  url='/conversations/:conversation_id/name'
  method='POST'
  title='Conversation Rename'
  name='#rename'
/>
<Row>
  <Col>
    ### Request Body
    Rename the session, the session name is used for display on clients that support multiple sessions.

    ### Path
    - `conversation_id` (string) Conversation ID

    <Properties>
      <Property name='name' type='string' key='name'>
        (Optional) The name of the conversation. This parameter can be omitted if `auto_generate` is set to `true`.
      </Property>
      <Property name='auto_generate' type='bool' key='auto_generate'>
        (Optional) Automatically generate the title, default is `false`
      </Property>
      <Property name='user' type='string' key='user'>
        The user identifier, defined by the developer, must ensure uniqueness within the application.
      </Property>
    </Properties>

    ### Response
    - `id` (string) Conversation ID
    - `name` (string) Conversation name
    - `inputs` (object) User input parameters
    - `status` (string) Conversation status
    - `introduction` (string) Introduction
    - `created_at` (timestamp) Creation timestamp, e.g., 1705395332
    - `updated_at` (timestamp) Update timestamp, e.g., 1705395332
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/conversations/:conversation_id/name" targetCode={`curl -X POST '${props.appDetail.api_base_url}/conversations/:conversation_id/name' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{ \n "name": "", \n "auto_generate": true, \n "user": "abc-123"\n}'`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/conversations/{conversation_id}/name' \
    --header 'Content-Type: application/json' \
    --header 'Authorization: Bearer {api_key}' \
    --data-raw '{
        "name": "",
        "auto_generate": true,
        "user": "abc-123"
    }'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
        "id": "cd78daf6-f9e4-4463-9ff2-54257230a0ce",
        "name": "Chat vs AI",
        "inputs": {},
        "status": "normal",
        "introduction": "",
        "created_at": 1705569238,
        "updated_at": 1705569238
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/conversations/:conversation_id/variables'
  method='GET'
  title='Get Conversation Variables'
  name='#conversation-variables'
/>
<Row>
  <Col>
    Retrieve variables from a specific conversation. This endpoint is useful for extracting structured data that was captured during the conversation.

    ### Path Parameters

    <Properties>
      <Property name='conversation_id' type='string' key='conversation_id'>
        The ID of the conversation to retrieve variables from.
      </Property>
    </Properties>

    ### Query Parameters

    <Properties>
      <Property name='user' type='string' key='user'>
        The user identifier, defined by the developer, must ensure uniqueness within the application
      </Property>
      <Property name='last_id' type='string' key='last_id'>
          (Optional) The ID of the last record on the current page, default is null.
      </Property>
      <Property name='limit' type='int' key='limit'>
          (Optional) How many records to return in one request, default is the most recent 20 entries. Maximum 100, minimum 1.
      </Property>
    </Properties>

    ### Response

    - `limit` (int) Number of items per page
    - `has_more` (bool) Whether there is a next page
    - `data` (array[object]) List of variables
      - `id` (string) Variable ID
      - `name` (string) Variable name
      - `value_type` (string) Variable type (string, number, object, etc.)
      - `value` (string) Variable value
      - `description` (string) Variable description
      - `created_at` (int) Creation timestamp
      - `updated_at` (int) Last update timestamp

    ### Errors
    - 404, `conversation_not_exists`, Conversation not found

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/conversations/:conversation_id/variables" targetCode={`curl -X GET '${props.appDetail.api_base_url}/conversations/{conversation_id}/variables?user=abc-123' \\\n--header 'Authorization: Bearer {api_key}'`}>

    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/conversations/{conversation_id}/variables?user=abc-123' \
    --header 'Authorization: Bearer {api_key}'
    ```

    </CodeGroup>

    <CodeGroup title="Request with variable name filter">
    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/conversations/{conversation_id}/variables?user=abc-123&variable_name=customer_name' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "limit": 100,
      "has_more": false,
      "data": [
        {
          "id": "variable-uuid-1",
          "name": "customer_name",
          "value_type": "string",
          "value": "John Doe",
          "description": "Customer name extracted from the conversation",
          "created_at": 1650000000000,
          "updated_at": 1650000000000
        },
        {
          "id": "variable-uuid-2",
          "name": "order_details",
          "value_type": "json",
          "value": "{\"product\":\"Widget\",\"quantity\":5,\"price\":19.99}",
          "description": "Order details from the customer",
          "created_at": 1650000000000,
          "updated_at": 1650000000000
        }
      ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/audio-to-text'
  method='POST'
  title='Speech to Text'
  name='#audio'
/>
<Row>
  <Col>
    This endpoint requires a multipart/form-data request.

    ### Request Body

    <Properties>
      <Property name='file' type='file' key='file'>
        Audio file.
        Supported formats: `['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm']`
        File size limit: 15MB
      </Property>
      <Property name='user' type='string' key='user'>
      User identifier, defined by the developer's rules, must be unique within the application.
      </Property>
    </Properties>

    ### Response
    - `text` (string) Output text

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/audio-to-text" targetCode={`curl -X POST '${props.appDetail.api_base_url}/audio-to-text' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'file=@localfile;type=audio/[mp3|mp4|mpeg|mpga|m4a|wav|webm]'`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/conversations/name' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'file=@localfile;type=audio/mp3'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "text": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/text-to-audio'
  method='POST'
  title='Text to Audio'
  name='#audio'
/>
<Row>
  <Col>
    Text to speech.

    ### Request Body

    <Properties>
      <Property name='message_id' type='str' key='message_id'>
        For text messages generated by Dify, simply pass the generated message-id directly. The backend will use the message-id to look up the corresponding content and synthesize the voice information directly. If both message_id and text are provided simultaneously, the message_id is given priority.
      </Property>
      <Property name='text' type='str' key='text'>
        Speech generated content。
      </Property>
      <Property name='user' type='string' key='user'>
        The user identifier, defined by the developer, must ensure uniqueness within the app.
      </Property>
    </Properties>
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/text-to-audio" targetCode={`curl --location --request POST '${props.appDetail.api_base_url}/text-to-audio' \\\n--header 'Authorization: Bearer ENTER-YOUR-SECRET-KEY' \\\n--form 'text=Hello Dify;user=abc-123;message_id=5ad4cb98-f0c7-4085-b384-88c403be6290`}>

    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.appDetail.api_base_url}/text-to-audio' \
    --header 'Authorization: Bearer ENTER-YOUR-SECRET-KEY' \
    --form 'file=Hello Dify;user=abc-123;message_id=5ad4cb98-f0c7-4085-b384-88c403be6290'
    ```

    </CodeGroup>

    <CodeGroup title="headers">
    ```json {{ title: 'headers' }}
    {
      "Content-Type": "audio/wav"
    }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/info'
  method='GET'
  title='Get Application Basic Information'
  name='#info'
/>
<Row>
  <Col>
  Used to get basic information about this application

  ### Response
  - `name` (string) application name
  - `description` (string) application description
  - `tags` (array[string]) application tags
  </Col>
  <Col>
    <CodeGroup title="Request" tag="GET" label="/info" targetCode={`curl -X GET '${props.appDetail.api_base_url}/info' \\\n-H 'Authorization: Bearer {api_key}'`}>
      ```bash {{ title: 'cURL' }}
      curl -X GET '${props.appDetail.api_base_url}/info' \
      -H 'Authorization: Bearer {api_key}'
      ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "name": "My App",
      "description": "This is my app.",
      "tags": [
        "tag1",
        "tag2"
      ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/parameters'
  method='GET'
  title='Get Application Parameters Information'
  name='#parameters'
/>
<Row>
  <Col>
    Used at the start of entering the page to obtain information such as features, input parameter names, types, and default values.

    ### Query

    <Properties>
      <Property name='user' type='string' key='user'>
          User identifier, defined by the developer's rules, must be unique within the application.
      </Property>
    </Properties>

    ### Response
    - `opening_statement` (string) Opening statement
    - `suggested_questions` (array[string]) List of suggested questions for the opening
    - `suggested_questions_after_answer` (object) Suggest questions after enabling the answer.
      - `enabled` (bool) Whether it is enabled
    - `speech_to_text` (object) Speech to text
      - `enabled` (bool) Whether it is enabled
    - `text_to_speech` (object) Text to speech
      - `enabled` (bool) Whether it is enabled
      - `voice` (string) Voice type
      - `language` (string) Language
      - `autoPlay` (string) Auto play
        - `enabled`   Enabled
        - `disabled`  Disabled
    - `retriever_resource` (object) Citation and Attribution
      - `enabled` (bool) Whether it is enabled
    - `annotation_reply` (object) Annotation reply
      - `enabled` (bool) Whether it is enabled
    - `user_input_form` (array[object]) User input form configuration
      - `text-input` (object) Text input control
        - `label` (string) Variable display label name
        - `variable` (string) Variable ID
        - `required` (bool) Whether it is required
        - `default` (string) Default value
      - `paragraph` (object) Paragraph text input control
        - `label` (string) Variable display label name
        - `variable` (string) Variable ID
        - `required` (bool) Whether it is required
        - `default` (string) Default value
      - `select` (object) Dropdown control
        - `label` (string) Variable display label name
        - `variable` (string) Variable ID
        - `required` (bool) Whether it is required
        - `default` (string) Default value
        - `options` (array[string]) Option values
    - `file_upload` (object) File upload configuration
      - `image` (object) Image settings
        Currently only supports image types: `png`, `jpg`, `jpeg`, `webp`, `gif`
        - `enabled` (bool) Whether it is enabled
        - `number_limits` (int) Image number limit, default is 3
        - `transfer_methods` (array[string]) List of transfer methods, remote_url, local_file, must choose one
    - `system_parameters` (object) System parameters
      - `file_size_limit` (int) Document upload size limit (MB)
      - `image_file_size_limit` (int) Image file upload size limit (MB)
      - `audio_file_size_limit` (int) Audio file upload size limit (MB)
      - `video_file_size_limit` (int) Video file upload size limit (MB)

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/parameters" targetCode={` curl -X GET '${props.appDetail.api_base_url}/parameters'`}>

    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/parameters' \
    --header 'Authorization: Bearer {api_key}'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "opening_statement": "Hello!",
      "suggested_questions_after_answer": {
          "enabled": true
      },
      "speech_to_text": {
          "enabled": true
      },
      "text_to_speech": {
          "enabled": true,
          "voice": "sambert-zhinan-v1",
          "language": "zh-Hans",
          "autoPlay": "disabled"
      },
      "retriever_resource": {
          "enabled": true
      },
      "annotation_reply": {
          "enabled": true
      },
      "user_input_form": [
          {
              "paragraph": {
                  "label": "Query",
                  "variable": "query",
                  "required": true,
                  "default": ""
              }
          }
      ],
      "file_upload": {
          "image": {
              "enabled": false,
              "number_limits": 3,
              "detail": "high",
              "transfer_methods": [
                  "remote_url",
                  "local_file"
              ]
          }
      },
      "system_parameters": {
          "file_size_limit": 15,
          "image_file_size_limit": 10,
          "audio_file_size_limit": 50,
          "video_file_size_limit": 100
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/meta'
  method='GET'
  title='Get Application Meta Information'
  name='#meta'
/>
<Row>
  <Col>
  Used to get icons of tools in this application

  ### Response
  - `tool_icons`(object[string]) tool icons
    - `tool_name` (string)
      - `icon` (object|string)
        - (object) icon object
          - `background` (string) background color in hex format
          - `content`(string) emoji
        - (string) url of icon
  </Col>
  <Col>
  <CodeGroup title="Request" tag="GET" label="/meta" targetCode={`curl -X GET '${props.appDetail.api_base_url}/meta' \\\n-H 'Authorization: Bearer {api_key}'`}>
    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/meta' \
    -H 'Authorization: Bearer {api_key}'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "tool_icons": {
        "dalle2": "https://cloud.dify.ai/console/api/workspaces/current/tool-provider/builtin/dalle/icon",
        "api_tool": {
          "background": "#252525",
          "content": "\ud83d\ude01"
        }
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/site'
  method='GET'
  title='Get Application WebApp Settings'
  name='#site'
/>
<Row>
  <Col>
  Used to get the WebApp settings of the application.
  ### Response
  - `title` (string) WebApp name
  - `chat_color_theme` (string) Chat color theme, in hex format
  - `chat_color_theme_inverted` (bool) Whether the chat color theme is inverted
  - `icon_type` (string) Icon type, `emoji` - emoji, `image` - picture
  - `icon` (string) Icon. If it's `emoji` type, it's an emoji symbol; if it's `image` type, it's an image URL
  - `icon_background` (string) Background color in hex format
  - `icon_url` (string) Icon URL
  - `description` (string) Description
  - `copyright` (string) Copyright information
  - `privacy_policy` (string) Privacy policy link
  - `custom_disclaimer` (string) Custom disclaimer
  - `default_language` (string) Default language
  - `show_workflow_steps` (bool) Whether to show workflow details
  - `use_icon_as_answer_icon` (bool) Whether to replace 🤖 in chat with the WebApp icon
  </Col>
  <Col>
  <CodeGroup title="Request" tag="POST" label="/meta" targetCode={`curl -X GET '${props.appDetail.api_base_url}/site' \\\n-H 'Authorization: Bearer {api_key}'`}>
    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/site' \
    -H 'Authorization: Bearer {api_key}'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "title": "My App",
      "chat_color_theme": "#ff4a4a",
      "chat_color_theme_inverted": false,
      "icon_type": "emoji",
      "icon": "😄",
      "icon_background": "#FFEAD5",
      "icon_url": null,
      "description": "This is my app.",
      "copyright": "all rights reserved",
      "privacy_policy": "",
      "custom_disclaimer": "All generated by AI",
      "default_language": "en-US",
      "show_workflow_steps": false,
      "use_icon_as_answer_icon": false,
    }
    ```
    </CodeGroup>
  </Col>
</Row>
___

<Heading
  url='/apps/annotations'
  method='GET'
  title='Get Annotation List'
  name='#annotation_list'
/>
<Row>
  <Col>
    ### Query
    <Properties>
      <Property name='page' type='string' key='page'>
        Page number
      </Property>
      <Property name='limit' type='string' key='limit'>
        Number of items returned, default 20, range 1-100
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/apps/annotations"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/apps/annotations?page=1&limit=20' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/apps/annotations?page=1&limit=20' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [
        {
          "id": "69d48372-ad81-4c75-9c46-2ce197b4d402",
          "question": "What is your name?",
          "answer": "I am Dify.",
          "hit_count": 0,
          "created_at": 1735625869
        }
      ],
      "has_more": false,
      "limit": 20,
      "total": 1,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/apps/annotations'
  method='POST'
  title='Create Annotation'
  name='#create_annotation'
/>
<Row>
  <Col>
    ### Query
    <Properties>
      <Property name='question' type='string' key='question'>
        Question
      </Property>
      <Property name='answer' type='string' key='answer'>
        Answer
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/apps/annotations"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/apps/annotations' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"question": "What is your name?","answer": "I am Dify."}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/apps/annotations' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "question": "What is your name?",
        "answer": "I am Dify."
    }'
    ```
    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      {
        "id": "69d48372-ad81-4c75-9c46-2ce197b4d402",
        "question": "What is your name?",
        "answer": "I am Dify.",
        "hit_count": 0,
        "created_at": 1735625869
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/apps/annotations/{annotation_id}'
  method='PUT'
  title='Update Annotation'
  name='#update_annotation'
/>
<Row>
  <Col>
    ### Query
    <Properties>
      <Property name='annotation_id' type='string' key='annotation_id'>
        Annotation ID
      </Property>
      <Property name='question' type='string' key='question'>
        Question
      </Property>
      <Property name='answer' type='string' key='answer'>
        Answer
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="PUT"
      label="/apps/annotations/{annotation_id}"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/apps/annotations/{annotation_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"question": "What is your name?","answer": "I am Dify."}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/apps/annotations/{annotation_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "question": "What is your name?",
        "answer": "I am Dify."
    }'
    ```
    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      {
        "id": "69d48372-ad81-4c75-9c46-2ce197b4d402",
        "question": "What is your name?",
        "answer": "I am Dify.",
        "hit_count": 0,
        "created_at": 1735625869
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/apps/annotations/{annotation_id}'
  method='DELETE'
  title='Delete Annotation'
  name='#delete_annotation'
/>
<Row>
  <Col>
    ### Query
    <Properties>
      <Property name='annotation_id' type='string' key='annotation_id'>
        Annotation ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="PUT"
      label="/apps/annotations/{annotation_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/apps/annotations/{annotation_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/apps/annotations/{annotation_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>

    <CodeGroup title="Response">
    ```text {{ title: 'Response' }}
    204 No Content
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/apps/annotation-reply/{action}'
  method='POST'
  title='Initial Annotation Reply Settings'
  name='#initial_annotation_reply_settings'
/>
<Row>
  <Col>
    ### Query
    <Properties>
      <Property name='action' type='string' key='action'>
        Action, can only be 'enable' or 'disable'
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        Specified embedding model provider, must be set up in the system first, corresponding to the provider field(Optional)
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        Specified embedding model, corresponding to the model field(Optional)
      </Property>
      <Property name='score_threshold' type='number' key='score_threshold'>
        The similarity threshold for matching annotated replies. Only annotations with scores above this threshold will be recalled.
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    The provider and model name of the embedding model can be obtained through the following interface: v1/workspaces/current/models/model-types/text-embedding. For specific instructions, see: Maintain Knowledge Base via API. The Authorization used is the Dataset API Token.
    <CodeGroup
      title="Request"
      tag="POST"
      label="/apps/annotation-reply/{action}"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/apps/annotation-reply/{action}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"score_threshold": 0.9, "embedding_provider_name": "zhipu", "embedding_model_name": "embedding_3"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST 'https://api.dify.ai/v1/apps/annotation-reply/{action}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "score_threshold": 0.9,
        "embedding_provider_name": "zhipu",
        "embedding_model_name": "embedding_3"
    }'
    ```
    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "job_id": "b15c8f68-1cf4-4877-bf21-ed7cf2011802",
      "job_status": "waiting"
    }
    ```
    </CodeGroup>
    This interface is executed asynchronously, so it will return a job_id. You can get the final execution result by querying the job status interface.
  </Col>
</Row>
---

<Heading
  url='/apps/annotation-reply/{action}/status/{job_id}'
  method='GET'
  title='Query Initial Annotation Reply Settings Task Status'
  name='#initial_annotation_reply_settings_task_status'
/>
<Row>
  <Col>
    ### Query
    <Properties>
    <Property name='action' type='string' key='action'>
        Action, can only be 'enable' or 'disable', must be the same as the action in the initial annotation reply settings interface
      </Property>
      <Property name='job_id' type='string' key='job_id'>
        Job ID, 
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/apps/annotations"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/apps/annotation-reply/{action}/status/{job_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/apps/annotation-reply/{action}/status/{job_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "job_id": "b15c8f68-1cf4-4877-bf21-ed7cf2011802",
      "job_status": "waiting",
      "error_msg": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>
