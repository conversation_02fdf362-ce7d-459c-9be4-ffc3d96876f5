.file {
  @apply box-border relative flex items-center justify-between;
  padding: 8px 12px 8px 8px;
  max-width: 640px;
  height: 40px;
  background: #ffffff;
  border: 0.5px solid #EAECF0;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.progressbar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #F2F4F7;
}

.file.uploading,
.file.uploading:hover {
  background: #FCFCFD;
  border: 0.5px solid #EAECF0;
}

.file.active {
  background: #F5F8FF;
  border: 1px solid #D1E0FF;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
}

.file:hover {
  background: #F5F8FF;
  border: 1px solid #D1E0FF;
  box-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

.fileIcon {
  @apply shrink-0 w-6 h-6 mr-2 bg-center bg-no-repeat;
  background-image: url(../assets/unknown.svg);
  background-size: 24px;
}

.fileIcon.csv {
  background-image: url(../assets/csv.svg);
}

.fileIcon.doc {
  background-image: url(../assets/doc.svg);
}

.fileIcon.docx {
  background-image: url(../assets/docx.svg);
}

.fileIcon.xlsx,
.fileIcon.xls {
  background-image: url(../assets/xlsx.svg);
}

.fileIcon.pdf {
  background-image: url(../assets/pdf.svg);
}

.fileIcon.html,
.fileIcon.htm {
  background-image: url(../assets/html.svg);
}

.fileIcon.md,
.fileIcon.markdown {
  background-image: url(../assets/md.svg);
}

.fileIcon.txt {
  background-image: url(../assets/txt.svg);
}

.fileIcon.json {
  background-image: url(../assets/json.svg);
}

.fileInfo {
  @apply grow flex items-center;
  z-index: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.filename {
  font-weight: 500;
  font-size: 13px;
  line-height: 18px;
  color: #1D2939;
}

.size {
  @apply ml-3;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #667085;
}

.actionWrapper {
  @apply flex items-center shrink-0;
  z-index: 1;
}

.actionWrapper .percent {
  font-weight: 400;
  font-size: 13px;
  line-height: 18px;
  color: #344054;
}

.actionWrapper .remove {
  display: none;
  width: 24px;
  height: 24px;
  background: center no-repeat url(../assets/trash.svg);
  background-size: 16px;
  cursor: pointer;
}

.file:hover .actionWrapper .remove {
  display: block;
}
