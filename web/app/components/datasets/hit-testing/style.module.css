.container {
  @apply flex h-full w-full relative overflow-y-auto;
}

.container>div {
  @apply flex-1 h-full;
}

.commonIcon {
  @apply w-3.5 h-3.5 inline-block align-middle;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}

.app_icon {
  background-image: url(./assets/grid.svg);
}

.hit_testing_icon {
  background-image: url(../documents/assets/target.svg);
}

.plugin_icon {
  background-image: url(./assets/plugin.svg);
}

.cardWrapper {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(284px, auto));
  grid-gap: 16px;
  grid-auto-rows: 216px;
}

.clockWrapper {
  border: 0.5px solid #eaecf5;
  @apply rounded-lg w-11 h-11 flex justify-center items-center;
}

.clockIcon {
  mask-image: url(./assets/clock.svg);
  @apply bg-gray-500;
}
