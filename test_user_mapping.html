<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户映射测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>用户映射功能测试</h1>
    
    <div class="test-section">
        <h2>1. 设置用户标识</h2>
        <input type="text" id="userIdInput" placeholder="输入用户名，如：张三" />
        <button onclick="setUserId()">设置用户标识</button>
        <div id="userIdResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试聊天消息API</h2>
        <input type="text" id="messageInput" placeholder="输入测试消息" />
        <button onclick="sendMessage()">发送消息</button>
        <div id="messageResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 查看用户映射</h2>
        <button onclick="showUserMappings()">显示用户映射</button>
        <div id="mappingResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 测试用户列表API</h2>
        <button onclick="fetchUserList()">获取用户列表</button>
        <div id="userListResult" class="result"></div>
    </div>

    <script>
        const APP_ID = 'GOILjRM4bIMNPnEo'; // 替换为实际的应用ID
        const API_BASE = 'http://127.0.0.1:5001';
        
        function setUserId() {
            const userId = document.getElementById('userIdInput').value;
            if (!userId) {
                alert('请输入用户名');
                return;
            }
            
            // 设置到localStorage
            localStorage.setItem('chat_user_id', userId);
            
            // 创建用户映射
            const userMappings = JSON.parse(localStorage.getItem('user_id_mappings') || '{}');
            // 这里我们假设用户ID就是用户名，实际应用中会从API获取真实的用户ID
            userMappings[userId] = userId;
            localStorage.setItem('user_id_mappings', JSON.stringify(userMappings));
            
            document.getElementById('userIdResult').textContent = `用户标识已设置: ${userId}`;
        }
        
        async function sendMessage() {
            const message = document.getElementById('messageInput').value;
            if (!message) {
                alert('请输入测试消息');
                return;
            }
            
            const userId = localStorage.getItem('chat_user_id');
            if (!userId) {
                alert('请先设置用户标识');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/v1/chat-messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${APP_ID}` // 简化的认证
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: message,
                        response_mode: 'blocking',
                        user: userId // 关键：添加用户参数
                    })
                });
                
                const result = await response.json();
                document.getElementById('messageResult').textContent = 
                    `发送成功!\n状态: ${response.status}\n响应: ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                document.getElementById('messageResult').textContent = 
                    `发送失败: ${error.message}`;
            }
        }
        
        function showUserMappings() {
            const userMappings = JSON.parse(localStorage.getItem('user_id_mappings') || '{}');
            const currentUserId = localStorage.getItem('chat_user_id');
            
            document.getElementById('mappingResult').textContent = 
                `当前用户ID: ${currentUserId}\n用户映射: ${JSON.stringify(userMappings, null, 2)}`;
        }
        
        async function fetchUserList() {
            try {
                const response = await fetch(`${API_BASE}/console/api/apps/7f034672-293c-4e5a-8241-41cd7ae4f279/users`, {
                    headers: {
                        'Authorization': 'Bearer your-console-token' // 需要控制台认证
                    }
                });
                
                const result = await response.json();
                document.getElementById('userListResult').textContent = 
                    `用户列表:\n${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                document.getElementById('userListResult').textContent = 
                    `获取用户列表失败: ${error.message}`;
            }
        }
        
        // 页面加载时显示当前状态
        window.onload = function() {
            showUserMappings();
        }
    </script>
</body>
</html>
